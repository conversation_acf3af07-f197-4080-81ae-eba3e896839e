from sqlalchemy import delete, update, text
from sqlalchemy import exc
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload

from records.db import base
from records.db import models
from records.utils import utils


@base.session_aware()
async def create_client_capitalization(values, session=None):
    client_capitalization = models.ClientCapitalization(**values)

    try:
        session.add(client_capitalization)
    except exc.SQLAlchemyError as e:
        raise RuntimeError(
            "Duplicate entry for ClientCapitalization: %s" % e
        )

    return client_capitalization


@base.session_aware()
async def update_client_capitalization(client_capitalization: models.ClientCapitalization, new_values: dict, session=None):
    new_values['updated_at'] = base.now()
    update_q = update(models.ClientCapitalization).where(models.ClientCapitalization.id == client_capitalization.id)
    update_q = update_q.values(new_values)

    await session.execute(update_q)

    client_capitalization.update(new_values)

    return client_capitalization


@base.session_aware()
async def delete_client_capitalization(id: int, session=None):
    delete_q = delete(models.ClientCapitalization).where(models.ClientCapitalization.id == id)
    await session.execute(delete_q)


@base.session_aware()
async def delete_client_capitalizations(ids: list[int] = None, client_id: str = None, session=None):
    return await base.delete_client_datas(models.ClientCapitalization, ids=ids, client_id=client_id, session=session)


@base.session_aware()
async def list_client_capitalizations(
    client_id: str,
    order: str = 'id',
    desc: bool = False,
    session=None
):
    query = select(models.ClientCapitalization).where(models.ClientCapitalization.client_id == client_id)
    query = query.options(joinedload(models.ClientCapitalization.person))
    query = query.options(joinedload(models.ClientCapitalization.share))

    if not hasattr(models.ClientCapitalization, order):
        order = 'id'
    order_col = getattr(models.ClientCapitalization, order)

    if desc:
        order_col = order_col.desc()
    query = query.order_by(order_col)
    res = await session.execute(query)

    res = res.scalars().fetchall()
    return res


@base.session_aware()
async def list_client_capitalizations_all(
    ids: list[int] = None,
    session=None
):
    query = select(models.ClientCapitalization)
    if ids:
        query = query.where(models.ClientCapitalization.id.in_(ids))

    res = await session.execute(query)

    return res.scalars().fetchall()


@base.session_aware()
async def get_client_capitalization_by_id(id: int, session=None):
    return await base.get_by_id(models.ClientCapitalization, id, session=session)
